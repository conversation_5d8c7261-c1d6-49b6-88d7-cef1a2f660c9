"""
Taifex Virtual Trading Platform Client

This module provides an asynchronous client for interacting with the Taifex Virtual Trading Platform.
It supports login, game management, WebSocket connections, and real-time data subscription.

Features:
    - Asynchronous HTTP and WebSocket communication
    - Automatic session management and authentication
    - Real-time market data subscription
    - Flexible logging configuration (default or custom logger)
    - SockJS protocol support for WebSocket connections
    - Comprehensive error handling and retry mechanisms

Example:
    Basic usage with context manager:

        import asyncio
        from taifex import Client

        async def main():
            async with <PERSON><PERSON>("account", "password") as client:
                await client.login()
                games = await client.get_game_list()
                if games:
                    await client.enter_game(games[0].game_id)
                    await client.connect_websocket()
                    await client.subscribe(["TXF"], ["QUOTE"])

        asyncio.run(main())
"""

import asyncio
import base64
import json
import logging
import random
import re
import time
import uuid
import zlib
from collections.abc import Coroutine
from typing import Any, Callable

import aiohttp
import websockets

from .response import GameInfo


class Client:
    """
    Asynchronous client for Taifex Virtual Trading Platform.

    This client provides functionality to:
    - Authenticate with the platform
    - Retrieve and enter trading games
    - Establish WebSocket connections for real-time data
    - Subscribe to market data feeds

    Attributes:
        base_url (str): The base URL of the Taifex platform
        account (str): User account for authentication
        password (str): User password for authentication
        ttb_version_str (str): TTB version string for compatibility

    Note:
        The client uses either a default logger or a custom logger provided during initialization.
        When using a custom logger, the set_log_level() method will not affect the logger's configuration.

        Security: All logging output is automatically sanitized to prevent sensitive information
        (such as session IDs, tokens, cookies, and passwords) from being logged. URLs, response
        texts, and payloads are filtered to mask potentially sensitive data.

    Example:
        Basic usage with default INFO logging:

            async with Client("account", "password") as client:
                await client.login()
                games = await client.get_game_list()
                await client.enter_game(games[0].game_id)
                await client.connect_websocket()

        Usage with DEBUG logging for detailed information:

            import logging
            async with Client("account", "password", log_level=logging.DEBUG) as client:
                await client.login()
                # ... rest of the code

        Change log level during runtime (only works with default logger):

            client = Client("account", "password")
            client.set_log_level(logging.WARNING)  # Only show warnings and errors

        Usage with custom logger for file output:

            import logging
            custom_logger = logging.getLogger("my_taifex_client")
            custom_logger.setLevel(logging.DEBUG)

            # Add file handler
            file_handler = logging.FileHandler("taifex_client.log")
            file_handler.setLevel(logging.DEBUG)
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            file_handler.setFormatter(formatter)
            custom_logger.addHandler(file_handler)

            # Use custom logger with client
            async with Client("account", "password", logger=custom_logger) as client:
                await client.login()  # Logs will be written to taifex_client.log
                # ... rest of the code
    """

    def __init__(
        self,
        account: str,
        password: str,
        base_url: str = "https://sim2.taifex.com.tw",
        ttb_version_str: str = "2025.05.02.0930",
        log_level: int = logging.INFO,
        logger: logging.Logger | None = None,
        proxy: str | None = None,
    ):
        """
        Initialize the Taifex client.

        Args:
            account (str): User account for authentication
            password (str): User password for authentication
            base_url (str, optional): Base URL of the platform. Defaults to "https://sim2.taifex.com.tw"
            ttb_version_str (str, optional): TTB version string. Defaults to "2025.05.02.0930"
            log_level (int, optional): Logging level for the default logger. Defaults to logging.INFO.
                                     Use logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR, etc.
                                     This parameter is ignored if a custom logger is provided.
            logger (logging.Logger, optional): Custom logger instance. If provided, the client will use
                                              this logger instead of creating its own. This allows for
                                              flexible logging configuration such as file output.

        Raises:
            ValueError: If any required parameter is empty
        """
        if not base_url or not account or not password:
            raise ValueError("base_url, account, password cannot be empty")

        self.base_url = base_url
        self.account = account
        self.password = password
        self.ttb_version_str = ttb_version_str
        self.proxy = proxy

        # Setup logger - use custom logger if provided, otherwise create default one
        if logger is not None:
            self.__logger = logger
            self.__is_custom_logger = True
        else:
            # Create instance-specific logger
            self.__logger = logging.getLogger(f"{__name__}.{id(self)}")
            self.__is_custom_logger = False
            self.__setup_logging(log_level)

        # Private attributes for session management
        self.__http_session: aiohttp.ClientSession | None = None
        self.__cookies = {}
        self.__jwt_token = None
        self.__ws_connection = None
        self.__ws_listener_task = None
        self.__message_callbacks = []
        self.__ws_session_uuid = None
        self.__background_tasks = set()

    def __setup_logging(self, log_level: int):
        """
        Configure logging for this client instance.

        Args:
            log_level (int): The logging level to set
        """
        # Configure the instance-specific logger
        self.__logger.setLevel(log_level)

        # Only add handler if none exists to avoid duplicate logs
        if not self.__logger.handlers:
            # Create console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)

            # Create formatter
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)

            # Add handler to logger
            self.__logger.addHandler(console_handler)
        else:
            # Update existing handlers' levels
            for handler in self.__logger.handlers:
                handler.setLevel(log_level)

    def __sanitize_url(self, url: str) -> str:
        """
        Sanitize URL for safe logging by masking sensitive parts.

        Args:
            url (str): The URL to sanitize

        Returns:
            str: Sanitized URL with sensitive parts masked
        """
        try:
            from urllib.parse import parse_qs, urlparse

            parsed = urlparse(url)

            # Mask session IDs in WebSocket URLs
            if "/ws/" in parsed.path:
                # Pattern: /wa/ws/{server_id}/{session_id}/websocket
                path_parts = parsed.path.split("/")
                if len(path_parts) >= 5 and path_parts[2] == "ws":
                    # Mask the session ID (index 3, which is the 4th part)
                    path_parts[3] = "***MASKED***"
                    parsed = parsed._replace(path="/".join(path_parts))
                elif len(path_parts) >= 6 and path_parts[3] == "ws":
                    # Alternative pattern: /wa/ws/{server_id}/{session_id}/websocket
                    path_parts[4] = "***MASKED***"
                    parsed = parsed._replace(path="/".join(path_parts))

            # Mask sensitive query parameters
            if parsed.query:
                query_params = parse_qs(parsed.query)
                sensitive_params = ["token", "session", "auth", "key"]
                for param in sensitive_params:
                    if param in query_params:
                        query_params[param] = ["***MASKED***"]

                # Reconstruct query string
                from urllib.parse import urlencode

                new_query = urlencode(query_params, doseq=True)
                parsed = parsed._replace(query=new_query)

            return parsed.geturl()

        except Exception:
            # If parsing fails, mask the entire URL after the domain
            if "://" in url:
                protocol_domain = url.split("/", 3)[:3]
                return "/".join(protocol_domain) + "/***MASKED***"
            return "***MASKED***"

    def __sanitize_response_text(self, text: str, max_length: int = 200) -> str:
        """
        Sanitize response text for safe logging.

        Args:
            text (str): The response text to sanitize
            max_length (int): Maximum length to log

        Returns:
            str: Sanitized response text
        """
        if not text:
            return text

        # Truncate first
        truncated = text[:max_length]

        # Mask common sensitive patterns

        # Mask JWT tokens (typically long base64 strings with dots)
        truncated = re.sub(r'"[Tt]oken":\s*"[A-Za-z0-9+/=._-]{50,}"', '"Token":"***MASKED***"', truncated)

        # Mask any JWT-like patterns (three base64 parts separated by dots)
        truncated = re.sub(
            r'"[A-Za-z0-9+/=]{20,}\.[A-Za-z0-9+/=]{20,}\.[A-Za-z0-9+/=_-]{20,}"', '"***MASKED***"', truncated
        )

        # Mask session IDs
        truncated = re.sub(r'"[Ss]ession[Ii][Dd]":\s*"[^"]{10,}"', '"SessionID":"***MASKED***"', truncated)

        # Mask cookies
        truncated = re.sub(r'"[Cc]ookie":\s*"[^"]{20,}"', '"Cookie":"***MASKED***"', truncated)

        # Mask any long base64-like strings (potential sensitive data) - but be more specific
        truncated = re.sub(r'"[A-Za-z0-9+/=]{60,}"', '"***MASKED***"', truncated)

        return truncated + ("..." if len(text) > max_length else "")

    def __sanitize_payload(self, payload_dict: dict) -> dict:
        """
        Sanitize payload dictionary for safe logging.

        Args:
            payload_dict (dict): The payload to sanitize

        Returns:
            dict: Sanitized payload with sensitive data masked
        """
        if not isinstance(payload_dict, dict):
            return payload_dict

        sanitized = payload_dict.copy()

        # Mask sensitive keys
        sensitive_keys = ["SessionID", "Token", "Cookie", "UserCyph", "Password", "Auth"]

        for key in sensitive_keys:
            if key in sanitized:
                sanitized[key] = "***MASKED***"

        return sanitized

    def set_log_level(self, log_level: int):
        """
        Change the logging level for this client instance.

        Note: This method only works when using the default logger. If a custom logger
        was provided during initialization, this method will have no effect and will
        log a warning message.

        Args:
            log_level (int): The new logging level (e.g., logging.DEBUG, logging.INFO, etc.)
        """
        if self.__is_custom_logger:
            self.__logger.warning(
                "Cannot change log level: client is using a custom logger. "
                "Please configure the log level on your custom logger directly."
            )
            return

        self.__setup_logging(log_level)

    async def __aenter__(self):
        """
        Async context manager entry.

        Initializes the HTTP session for the client.

        Returns:
            Client: The client instance for use in the context
        """
        await self.init_session()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """
        Async context manager exit.

        Properly closes all connections and cleans up resources.

        Args:
            exc_type: Exception type (if any)
            exc_value: Exception value (if any)
            traceback: Exception traceback (if any)
        """
        await self.close()

    async def init_session(self):
        """
        Initialize HTTP session if not already created.

        Creates a new aiohttp.ClientSession for making HTTP requests.
        """
        if self.__http_session is None or self.__http_session.closed:
            if self.proxy:
                self.__http_session = aiohttp.ClientSession(proxy=self.proxy)
            else:
                self.__http_session = aiohttp.ClientSession()
            self.__logger.info("aiohttp.ClientSession created")

    async def __ensure_session(self) -> aiohttp.ClientSession:
        """
        Ensure HTTP session is available and return it.

        Returns:
            aiohttp.ClientSession: The active HTTP session
        """
        if self.__http_session is None or self.__http_session.closed:
            await self.init_session()
            assert self.__http_session is not None
        return self.__http_session

    def __create_websocket_session_id_encoded(self):
        """
        Create WebSocket session ID with UUID and platform-specific prefix.

        Generates a UUID with "branch-" prefix and random number to match GUI SDK format.
        This format is used by the OMS WebSocket endpoint.

        Returns:
            str: Session ID in format "branch-{random_number}${uuid}"
        """
        self.__ws_session_uuid = str(uuid.uuid4())
        # Use the same format as GUI SDK: branch-{random_number}${uuid}
        branch_number = random.randint(10000, 99999)
        session_id = f"branch-{branch_number}${self.__ws_session_uuid}"
        return session_id

    def __is_heartbeat_or_session_message(self, data: dict) -> bool:
        """
        Check if the message is a heartbeat or session maintenance message.

        Args:
            data (dict): The parsed message data

        Returns:
            bool: True if the message is a heartbeat or session message
        """
        # Check for direct heartbeat format: {'hb': 'timestamp'}
        if isinstance(data, dict) and "hb" in data:
            return True

        # Check for heartbeat format: {'heartbeat': 'timestamp'}
        if isinstance(data, dict) and "heartbeat" in data:
            return True

        # Check for compressed heartbeat format: {'payload': {'hb': ...}}
        if "payload" in data and isinstance(data["payload"], dict) and "hb" in data["payload"]:
            return True

        # Check for compressed heartbeat format: {'payload': {'heartbeat': ...}}
        if "payload" in data and isinstance(data["payload"], dict) and "heartbeat" in data["payload"]:
            return True

        # Check for session maintenance messages (SessionID + Hostname only)
        if isinstance(data, dict) and "SessionID" in data and "Hostname" in data and len(data) == 2:
            return True

        # Check for other known heartbeat patterns
        return bool(isinstance(data, dict) and data.get("type") == "heartbeat")

    async def login(self):
        """
        Authenticate with the Taifex platform.

        Performs login using account credentials and obtains JWT token and cookies
        for subsequent API calls.

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If login request fails with non-200 status code
        """
        session = await self.__ensure_session()

        login_url = f"{self.base_url}/api2/User/UserLogin"
        login_payload = {
            "UserMail": self.account,
            "UserCyph": self.password,
            "LoginType": "TTB",
            "TTBVer": self.ttb_version_str,
        }
        headers = {
            "Content-Type": "application/json; charset=utf-8",
        }

        self.__logger.info(f"Logging in to {login_url}")
        async with session.post(login_url, json=login_payload, headers=headers) as response:
            response_text = await response.text()
            self.__logger.info(f"Login Status: {response.status}")

            if response.status == 200:
                try:
                    login_data = json.loads(response_text)
                    if login_data.get("Code") == "0000":
                        self.__jwt_token = login_data.get("Token")
                        self.__logger.info("Login successful. JWT Token obtained.")
                        # Store cookies for subsequent requests
                        for key, value in response.cookies.items():
                            self.__cookies[key] = value
                        self.__logger.info("Cookies obtained.")
                except json.JSONDecodeError as e:
                    # Sanitize response text before logging
                    safe_response = self.__sanitize_response_text(response_text)
                    self.__logger.error(f"Failed to decode JSON. Response text: {safe_response}")
                    self.__logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                # Sanitize response text before logging
                safe_response = self.__sanitize_response_text(response_text)
                self.__logger.error(
                    f"Login request failed. Http status: {response.status}. Response text: {safe_response}",
                )
                raise Exception(f"Login request failed, Http status code: {response.status}")

    async def get_game_list(self) -> list[GameInfo]:
        """
        Retrieve list of available trading games.

        Fetches the list of trading games available on the platform.
        Automatically attempts login if JWT token is not available.

        Returns:
            list[GameInfo]: List of available games as GameInfo objects

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            self.__logger.warning("JWT Token not found, attempting to login...")
            await self.login()

        game_list_url = f"{self.base_url}/api2/Game/GameList"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }

        self.__logger.info(f"Getting game list from {game_list_url}")
        async with session.post(game_list_url, json={}, headers=headers) as response:
            response_text = await response.text()
            self.__logger.info(f"Game List Status: {response.status}")

            if response.status == 200:
                try:
                    game_data = json.loads(response_text)
                    if game_data.get("Code") == "0000":
                        self.__logger.info("Game list obtained successfully")
                        return [GameInfo.from_dict(game) for game in game_data.get("Data", {}).get("Games", [])]
                    else:
                        self.__logger.error(f"Get game list failed: {game_data.get('Message')}")
                        raise Exception(f"Get game list failed: {game_data.get('Message')}")
                except json.JSONDecodeError as e:
                    self.__logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    self.__logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                self.__logger.error(
                    f"Get game list failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Get game list failed, Http status code: {response.status}, response: {response_text}")

    async def enter_game(self, game_id: str):
        """
        Enter a specific trading game.

        Joins the specified trading game using the game ID.
        Automatically attempts login if JWT token is not available.

        Args:
            game_id (str): The ID of the game to enter

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If enter game request fails with non-200 status code
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            self.__logger.warning("JWT Token not found, attempting to login...")
            await self.login()

        game_enter_url = f"{self.base_url}/api2/Game/GameEnter"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }
        payload = {
            "GameID": game_id,
            "LoginType": "TTB",
        }

        self.__logger.info(f"Entering game {game_id}")
        async with session.post(game_enter_url, json=payload, headers=headers) as response:
            response_text = await response.text()
            self.__logger.info(f"Game Enter Status: {response.status}")

            if response.status == 200:
                try:
                    enter_data = json.loads(response_text)
                    if enter_data.get("Code") == "0000":
                        self.__logger.info(f"Successfully entered game: {game_id}")
                    else:
                        self.__logger.error(f"Enter game failed: {enter_data.get('Message')}")
                except json.JSONDecodeError as e:
                    self.__logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    self.__logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                self.__logger.error(
                    f"Enter game failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Enter game failed, Http status code: {response.status}")

    async def connect_websocket(self):
        """
        Establish WebSocket connection and start message listening.

        Creates a WebSocket connection to the platform for real-time data streaming.
        Uses SockJS protocol with platform-specific session management.

        Raises:
            Exception: If WebSocket connection fails
        """
        if self.__ws_connection is not None:
            self.__logger.info("WebSocket connection already established. Reusing the existing connection.")
            return

        await self.__get_sockjs_info()

        ws_session_id = self.__create_websocket_session_id_encoded()
        server_id = str(random.randint(100, 999))
        # Use the correct OMS WebSocket endpoint that matches GUI SDK
        websocket_uri = f"wss://{self.base_url.split('//')[1]}/wa/oms2WS/{server_id}/{ws_session_id}/websocket"

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        ws_headers = {
            "Cookie": cookie_str,
            "Origin": self.base_url,
        }
        # Sanitize WebSocket URI before logging
        safe_uri = self.__sanitize_url(websocket_uri)
        self.__logger.info(f"Connecting to WebSocket: {safe_uri}...")

        try:
            self.__ws_connection = await websockets.connect(
                websocket_uri,
                additional_headers=ws_headers,
                user_agent_header=None,  # Prevent automatic user-agent header addition
                origin=None,  # Prevent automatic origin header addition
            )
            self.__logger.info("WebSocket Connection Established.")
            # Start background task for message listening
            self.__ws_listener_task = asyncio.create_task(self.__listen_websocket())

        except websockets.exceptions.WebSocketException as e:
            self.__logger.error(f"WebSocket Connection Failed: {e}")
            self.__ws_connection = None
            raise Exception(f"Connect WebSocket Failed: {e}") from e

    def register_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Register a callback function for WebSocket message handling.

        The callback will be invoked for each received WebSocket message.
        Multiple callbacks can be registered and they will all be called for each message.

        Args:
            callback: Async function to handle received messages.
                     Must have signature: async def callback(message: dict[str, Any]) -> None
                     The message dict contains 'type' and 'payload' keys.

        Example:
            async def my_callback(message):
                if message['type'] == 'data':
                    print(f"Received data: {message['payload']}")

            client.register_message_callback(my_callback)
        """
        if not asyncio.iscoroutinefunction(callback):
            self.__logger.warning(
                f"Callback function {callback.__name__} is not a coroutine function. It may cause blocking."
            )
        self.__logger.info(f"Registered callback function {callback.__name__}")
        self.__message_callbacks.append(callback)

    def unregister_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Remove a previously registered callback function.

        Args:
            callback: The callback function to remove
        """
        try:
            self.__message_callbacks.remove(callback)
            self.__logger.info(f"Unregistered callback function {callback.__name__}")
        except ValueError:
            self.__logger.warning(f"Callback function {callback.__name__} is not registered")

    async def subscribe(self, products: list[str]) -> bool:
        """
        Subscribe to market data feeds for specified products and data types.

        Establishes subscription for real-time market data. Automatically connects
        WebSocket if not already connected.

        Args:
            products (list[str]): List of product codes to subscribe to (e.g., ["TXFF5.TW-A", "MXFF5.TW-A"])

        Returns:
            bool: True if subscription successful, False otherwise

        Raises:
            Exception: If WebSocket connection fails or session UUID is missing

        Example:
            success = await client.subscribe(["TXFF5"])
            if success:
                print("Subscription successful")
        """
        session = await self.__ensure_session()

        if not self.__ws_connection:
            self.__logger.warning("WebSocket not connected, attempting to connect...")
            await self.connect_websocket()
            if not self.__ws_connection:
                raise Exception("WebSocket connection failed, cannot subscribe.")

        if not self.__ws_session_uuid:
            self.__logger.error("WebSocket session UUID not found, cannot subscribe.")
            raise Exception("WebSocket session UUID lost")

        # Use the correct API endpoint for registration (try the original path first)
        reg_url = f"{self.base_url}/wa/api/reg"
        reg_payload_dict = {
            "SessionID": self.__ws_session_uuid,
            "Prods": products,
            "Types": ["Tick", "BA"],
        }
        reg_payload_str = json.dumps(reg_payload_dict, ensure_ascii=False)
        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        reg_headers = {
            "Content-Type": "application/x-www-form-urlencoded",  # Platform-specific requirement
            "Cookie": cookie_str,
        }

        self.__logger.info(f"Subscribing to {reg_url}...")
        # Sanitize payload before logging
        safe_payload = self.__sanitize_payload(reg_payload_dict)
        self.__logger.info(f"Subscription Payload: {safe_payload}")

        async with session.post(reg_url, data=reg_payload_str.encode("utf-8"), headers=reg_headers) as response:
            response_text = await response.text()
            self.__logger.info(f"Subscription Status: {response.status}")
            # Sanitize response before logging
            safe_response = self.__sanitize_response_text(response_text)
            self.__logger.info(f"Subscription Response: {safe_response}")
            if response.status == 200 and '"result":"success"' in response_text.lower():
                self.__logger.info("Subscription successful.")
                return True
            else:
                safe_error_response = self.__sanitize_response_text(response_text)
                self.__logger.error(f"Subscription {products} failed: {safe_error_response}")
                return False

    async def close(self):
        """
        Clean up and close all connections.

        Properly closes WebSocket connection, cancels background tasks,
        and closes HTTP session to free resources.
        """
        self.__logger.info("Preparing to close client connections...")

        # Cancel WebSocket listener task
        if self.__ws_listener_task:
            self.__ws_listener_task.cancel()
            try:
                await self.__ws_listener_task
            except asyncio.CancelledError:
                self.__logger.info("WebSocket listener task cancelled.")

        # Close WebSocket connection
        if self.__ws_connection:
            self.__logger.info("Closing WebSocket connection...")
            await self.__ws_connection.close()
            self.__ws_connection = None
            self.__logger.info("WebSocket connection closed.")

        # Close HTTP session
        if self.__http_session and not self.__http_session.closed:
            self.__logger.info("Closing aiohttp.ClientSession...")
            await self.__http_session.close()
            self.__logger.info("aiohttp.ClientSession closed.")

        self.__logger.info("Client closed successfully.")

    async def __get_sockjs_info(self):
        """
        Retrieve SockJS connection information.

        Fetches SockJS metadata required for WebSocket connection establishment.
        Updates cookies if provided in the response.

        Returns:
            dict: SockJS connection information

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If request fails with non-200 status code
        """
        session = await self.__ensure_session()

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        timestamp = int(time.time() * 1000)
        # Use the correct OMS WebSocket endpoint that matches GUI SDK
        info_url = f"{self.base_url}/wa/oms2WS/info?t={timestamp}"
        info_headers = {"Cookie": cookie_str}

        # Sanitize URL before logging
        safe_info_url = self.__sanitize_url(info_url)
        self.__logger.info(f"Fetching SockJS info from {safe_info_url}...")

        async with session.get(info_url, headers=info_headers) as response:
            response_text = await response.text()
            # Sanitize response before logging
            safe_response = self.__sanitize_response_text(response_text)
            self.__logger.debug(f"SockJS Info Response: {safe_response}")
            if response.status == 200:
                try:
                    info_data = json.loads(response_text)
                    # Only log non-sensitive parts of the info data
                    safe_info = {k: v for k, v in info_data.items() if k not in ["entropy", "session"]}
                    self.__logger.info(f"SockJS Info retrieved successfully: {safe_info}")
                    # Some APIs may update cookies here
                    for key, value in response.cookies.items():
                        self.__cookies[key] = value
                    return info_data
                except json.JSONDecodeError as e:
                    safe_error_response = self.__sanitize_response_text(response_text)
                    self.__logger.error(f"SockJS Info is not valid JSON: {safe_error_response}")
                    raise e
            else:
                self.__logger.error(f"Fetch SockJS info Failed, HTTP Status: {response.status}")
                raise Exception(f"Fetch SockJS info Failed, HTTP Status: {response.status}")

    async def __listen_websocket(self):
        """
        Listen to WebSocket messages and process them.

        Handles incoming WebSocket messages using SockJS protocol.
        Supports multiple message formats including:
        - SockJS control messages (open, heartbeat)
        - JSON data messages
        - Compressed data (zlib/base64 encoded)

        Invokes registered callbacks for each processed message.
        """
        if not self.__ws_connection:
            self.__logger.error("WebSocket connection is not established. Cannot listen to messages.")
            return

        self.__logger.info("Start listening to WebSocket messages...")
        try:
            async for message in self.__ws_connection:
                self.__logger.debug(f"Received WebSocket message: {message}")
                parsed_data = None

                # Handle SockJS control messages
                if message == "o":
                    self.__logger.info("SockJS connection established.")
                    continue
                elif message == "h":
                    self.__logger.debug("Heartbeat received.")
                    continue
                elif isinstance(message, str) and message.startswith("a["):
                    try:
                        # SockJS array frame format: 'a["json_string_payload"]'
                        payload_wrapper_str = message[1:]  # Remove leading 'a'
                        payload_list = json.loads(payload_wrapper_str)

                        if payload_list and isinstance(payload_list, list):
                            for item_str in payload_list:
                                try:
                                    # Try direct JSON parsing first
                                    actual_data = json.loads(item_str)

                                    # Check if this is a heartbeat or session message
                                    if self.__is_heartbeat_or_session_message(actual_data):
                                        self.__logger.debug(f"Heartbeat/session message received: {actual_data}")
                                        continue

                                    parsed_data = {"type": "data", "payload": actual_data}
                                    self.__logger.info(f"Successfully parsed SockJS array frame: {actual_data}")
                                except json.JSONDecodeError:
                                    self.__logger.debug(
                                        "Failed to parse item as direct JSON, attempting zlib/base64 decompression. "
                                        + f"Item: {item_str[:60]}..."
                                    )
                                    try:
                                        # Try base64 -> zlib -> json decompression
                                        decoded_base64 = base64.b64decode(item_str)
                                        try:
                                            decompressed_data_bytes = zlib.decompress(decoded_base64)
                                        except zlib.error as zde:
                                            # If standard zlib fails, try raw deflate
                                            self.__logger.debug(
                                                f"Standard zlib.decompress failed ({zde}), trying raw deflate."
                                            )
                                            decompress_obj = zlib.decompressobj(-zlib.MAX_WBITS)
                                            decompressed_data_bytes = decompress_obj.decompress(decoded_base64)
                                            decompressed_data_bytes += decompress_obj.flush()

                                        decompressed_json_str = decompressed_data_bytes.decode("utf-8")
                                        actual_data = json.loads(decompressed_json_str)

                                        self.__logger.debug(f"Successfully parsed compressed data: {actual_data}")
                                        # Check if this is a heartbeat or session message
                                        if self.__is_heartbeat_or_session_message(actual_data):
                                            self.__logger.debug(f"Heartbeat/session message received: {actual_data}")
                                            continue

                                    except Exception as e_zlib:
                                        # Decompression failed, treat as raw data
                                        self.__logger.warning(
                                            f"Failed to parse item using zlib/base64. Item: {item_str[:60]}. "
                                            + f"Error: {e_zlib}"
                                        )
                                        parsed_data = {"type": "raw_sockjs_array_item", "payload": item_str}
                        else:
                            parsed_data = {"type": "unknown_sockjs_array", "payload": payload_list}

                    except json.JSONDecodeError:
                        self.__logger.warning(f"Failed to parse SockJS array frame (outer layer): {message}")
                        parsed_data = {"type": "raw_message", "payload": message}
                else:
                    # Handle non-SockJS messages
                    self.__logger.info(f"Received unknown type or normal WebSocket message: {message}")
                    parsed_data = {"type": "unknown", "payload": message}

                # Invoke all registered callbacks
                if parsed_data:
                    for callback in self.__message_callbacks:
                        task = asyncio.create_task(callback(parsed_data))
                        self.__background_tasks.add(task)
                        task.add_done_callback(self.__background_tasks.discard)

        except websockets.exceptions.ConnectionClosed as e:
            self.__logger.warning(f"WebSocket connection closed, code: {e.code}, reason: {e.reason}")
        except Exception as e:
            self.__logger.error(f"Error in WebSocket listener: {e}", exc_info=True)
        finally:
            self.__logger.info("WebSocket listener stopped.")
            self.__ws_connection = None  # Mark connection as closed
