import asyncio
import logging
import os

import dotenv

from src.taifex import Client


async def test_handler(data):
    # Only print non-heartbeat messages
    if data.get("type") == "data":
        payload = data.get("payload", {})
        fcode = payload.get("FCODE", "")
        if fcode not in ["21"]:  # Skip heartbeat messages (FCODE 21)
            print("===========")
            print("TEST_HANDLER:")
            print(data)
            print("===========")


async def main() -> None:
    dotenv.load_dotenv()

    account = str(os.getenv("ACCOUNT"))
    password = str(os.getenv("PASSWORD"))

    async with Client(account, password, log_level=logging.INFO) as client:
        await client.login()
        games = await client.get_game_list()
        print(f"Available games: {[game.game_name for game in games]}")
        await client.enter_game(games[0].game_id)
        await client.connect_websocket()

        # Try different product codes
        products_to_try = ["TXF", "TXFF5", "TXFF5.TW-A", "MXF", "MXFF5"]
        for product in products_to_try:
            print(f"Trying to subscribe to: {product}")
            success = await client.subscribe([product])
            if success:
                print(f"Successfully subscribed to {product}")
                break
            else:
                print(f"Failed to subscribe to {product}")

        client.register_message_callback(test_handler)

        print("Waiting for market data... (60 seconds)")
        await asyncio.sleep(60)


if __name__ == "__main__":
    asyncio.run(main())
