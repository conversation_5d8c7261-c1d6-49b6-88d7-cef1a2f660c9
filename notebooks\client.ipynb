{"cells": [{"cell_type": "code", "execution_count": 1, "id": "00ba00e3", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")"]}, {"cell_type": "code", "execution_count": 2, "id": "a14a8bcc", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import dotenv\n", "dotenv.load_dotenv()"]}, {"cell_type": "code", "execution_count": 3, "id": "b9240f97", "metadata": {}, "outputs": [], "source": ["import os\n", "ACCOUNT = str(os.getenv(\"ACCOUNT\"))\n", "PASSWORD = str(os.getenv(\"PASSWORD\"))\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a1082825", "metadata": {}, "outputs": [], "source": ["from src.taifex import Client"]}, {"cell_type": "code", "execution_count": 5, "id": "4af9829f", "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "\n", "client = Client(ACCOUNT, PASSWORD, log_level=logging.DEBUG)"]}, {"cell_type": "code", "execution_count": 6, "id": "9484d751", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:13,764 - src.taifex.************* - INFO - aiohttp.ClientSession created\n"]}], "source": ["await client.init_session()"]}, {"cell_type": "code", "execution_count": 7, "id": "106a32f5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:13,782 - src.taifex.************* - INFO - Logging in to https://sim2.taifex.com.tw/api2/User/UserLogin\n", "2025-05-26 13:32:14,583 - src.taifex.************* - INFO - Login Status: 200\n", "2025-05-26 13:32:14,589 - src.taifex.************* - INFO - <PERSON><PERSON> successful. JWT <PERSON> obtained.\n", "2025-05-26 13:32:14,591 - src.taifex.************* - INFO - Cookies obtained.\n"]}], "source": ["await client.login()"]}, {"cell_type": "code", "execution_count": 8, "id": "2f198ddb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:14,605 - src.taifex.************* - INFO - Getting game list from https://sim2.taifex.com.tw/api2/Game/GameList\n", "2025-05-26 13:32:14,739 - src.taifex.************* - INFO - Game List Status: 200\n", "2025-05-26 13:32:14,740 - src.taifex.************* - INFO - Game list obtained successfully\n"]}], "source": ["games = await client.get_game_list()"]}, {"cell_type": "code", "execution_count": 9, "id": "ca7f43e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[GameInfo(date_begin='2024-04-08', date_end='2099-12-31', date_duration='2024-04-08 ~ 2099-12-31', game_id='240403001', game_name='盤中延遲模式', market_mode='TAIFEX-A', market_name='盤中延遲5分鐘'),\n", " GameInfo(date_begin='2024-04-08', date_end='2099-12-31', date_duration='2024-04-08 ~ 2099-12-31', game_id='240403002', game_name='午間重播模式', market_mode='TAIFEX-B', market_name='午間重播'),\n", " GameInfo(date_begin='2024-04-08', date_end='2099-12-31', date_duration='2024-04-08 ~ 2099-12-31', game_id='240403003', game_name='晚間重播模式', market_mode='TAIFEX-C', market_name='晚間重播'),\n", " GameInfo(date_begin='2025-04-28', date_end='2025-05-11', date_duration='2025-05-12 ~ 2025-07-16', game_id='250331001', game_name='114大專院校模擬競賽初賽', market_mode='TAIFEX', market_name='即時')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["games\n"]}, {"cell_type": "code", "execution_count": 10, "id": "a719e5e9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:14,788 - src.taifex.************* - INFO - Entering game 240403001\n", "2025-05-26 13:32:14,959 - src.taifex.************* - INFO - Game Enter Status: 200\n", "2025-05-26 13:32:14,960 - src.taifex.************* - INFO - Successfully entered game: 240403001\n"]}], "source": ["await client.enter_game(games[0].game_id)"]}, {"cell_type": "code", "execution_count": 11, "id": "a4bacad4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:14,968 - src.taifex.************* - INFO - Registered callback function example_handler\n"]}], "source": ["async def example_handler(data):\n", "    print(\"===========\")\n", "    print(\"EXAMPLE_HANDLER:\")\n", "    print(data)\n", "    print(\"===========\")\n", "\n", "client.register_message_callback(example_handler)"]}, {"cell_type": "code", "execution_count": null, "id": "d0fb1c4f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:14,993 - src.taifex.************* - INFO - Fetching SockJS info from https://sim2.taifex.com.tw/wa/ws/info?t=1748237534992...\n", "2025-05-26 13:32:15,112 - src.taifex.************* - DEBUG - SockJS Info Response: {\"websocket\":true,\"cookie_needed\":false,\"origins\":[\"*:*\"],\"entropy\":566089061}\n", "\n", "2025-05-26 13:32:15,113 - src.taifex.************* - INFO - SockJS Info retrieved successfully: {'websocket': True, 'cookie_needed': False, 'origins': ['*:*']}\n", "2025-05-26 13:32:15,114 - src.taifex.************* - INFO - Connecting to WebSocket: wss://sim2.taifex.com.tw/wa/ws/***MASKED***/YjhjODgxNWQtMzMzOC00N2IzLTgwNmQtNjA4OTdlNzBlOGIwJGFwZXhAdHc=/websocket...\n", "2025-05-26 13:32:15,616 - src.taifex.************* - INFO - WebSocket Connection Established.\n", "2025-05-26 13:32:15,617 - src.taifex.************* - INFO - Start listening to WebSocket messages...\n", "2025-05-26 13:32:15,620 - src.taifex.************* - DEBUG - Received WebSocket message: o\n", "2025-05-26 13:32:15,620 - src.taifex.************* - INFO - SockJS connection established.\n", "2025-05-26 13:32:15,621 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"SessionID\\\":\\\"b8c8815d-3338-47b3-806d-60897e70e8b0\\\", \\\"Hostname\\\":\\\"newsimqote2\\\"}\"]\n", "2025-05-26 13:32:15,622 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'SessionID': 'b8c8815d-3338-47b3-806d-60897e70e8b0', 'Hostname': 'newsimqote2'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:16,635 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MTguMDAwIn0BAAD//87wCcQ=\"]\n", "2025-05-26 13:32:16,639 - src.taifex.************* - DEBUG - Failed to parse item as direct JSON, attempting zlib/base64 decompression. Item: eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MTguMDAwIn0B...\n", "2025-05-26 13:32:16,641 - src.taifex.************* - DEBUG - Successfully parsed compressed data: {'heartbeat': '20250526_13:32:18.000'}\n", "2025-05-26 13:32:16,642 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'heartbeat': '20250526_13:32:18.000'}\n", "2025-05-26 13:32:18,607 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:20.118\\\"}\"]\n", "2025-05-26 13:32:18,609 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:20.118'}\n", "2025-05-26 13:32:21,645 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MjMuMDAwIn0BAAD//87VCcA=\"]\n", "2025-05-26 13:32:21,647 - src.taifex.************* - DEBUG - Failed to parse item as direct JSON, attempting zlib/base64 decompression. Item: eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MjMuMDAwIn0B...\n", "2025-05-26 13:32:21,649 - src.taifex.************* - DEBUG - Successfully parsed compressed data: {'heartbeat': '20250526_13:32:23.000'}\n", "2025-05-26 13:32:21,651 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'heartbeat': '20250526_13:32:23.000'}\n", "2025-05-26 13:32:21,653 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:23.119\\\"}\"]\n", "2025-05-26 13:32:21,655 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:23.119'}\n", "2025-05-26 13:32:24,828 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:26.120\\\"}\"]\n", "2025-05-26 13:32:24,829 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:26.120'}\n", "2025-05-26 13:32:26,673 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MjguMDAwIn0BAAD//874CcU=\"]\n", "2025-05-26 13:32:26,674 - src.taifex.************* - DEBUG - Failed to parse item as direct JSON, attempting zlib/base64 decompression. Item: eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MjguMDAwIn0B...\n", "2025-05-26 13:32:26,675 - src.taifex.************* - DEBUG - Successfully parsed compressed data: {'heartbeat': '20250526_13:32:28.000'}\n", "2025-05-26 13:32:26,676 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'heartbeat': '20250526_13:32:28.000'}\n", "2025-05-26 13:32:27,612 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:29.120\\\"}\"]\n", "2025-05-26 13:32:27,614 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:29.120'}\n", "2025-05-26 13:32:30,638 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:32.120\\\"}\"]\n", "2025-05-26 13:32:30,644 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:32.120'}\n", "2025-05-26 13:32:31,748 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MzMuMDAwIn0BAAD//87dCcE=\"]\n", "2025-05-26 13:32:31,749 - src.taifex.************* - DEBUG - Failed to parse item as direct JSON, attempting zlib/base64 decompression. Item: eAEAJQDa/3siaGVhcnRiZWF0IjoiMjAyNTA1MjZfMTM6MzI6MzMuMDAwIn0B...\n", "2025-05-26 13:32:31,750 - src.taifex.************* - DEBUG - Successfully parsed compressed data: {'heartbeat': '20250526_13:32:33.000'}\n", "2025-05-26 13:32:31,750 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'heartbeat': '20250526_13:32:33.000'}\n", "2025-05-26 13:32:33,636 - src.taifex.************* - DEBUG - Received WebSocket message: a[\"{\\\"hb\\\":\\\"2025/05/26 13:32:35.121\\\"}\"]\n", "2025-05-26 13:32:33,638 - src.taifex.************* - DEBUG - Heartbeat/session message received: {'hb': '2025/05/26 13:32:35.121'}\n"]}], "source": ["await client.connect_websocket()"]}, {"cell_type": "code", "execution_count": 13, "id": "198e18ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 13:32:15,639 - src.taifex.************* - INFO - Subscribing to https://sim2.taifex.com.tw/wa/api/reg...\n", "2025-05-26 13:32:15,639 - src.taifex.************* - INFO - Subscription Payload: {'SessionID': '***MASKED***', 'Prods': ['TXFF5.TW-A'], 'Types': ['Tick', 'BA']}\n", "2025-05-26 13:32:15,740 - src.taifex.************* - INFO - Subscription Status: 200\n", "2025-05-26 13:32:15,741 - src.taifex.************* - INFO - Subscription Response: {\"result\":\"success\"}\n", "2025-05-26 13:32:15,742 - src.taifex.************* - INFO - Subscription successful.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["await client.subscribe([\"TXFF5.TW-A\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9957bd09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}